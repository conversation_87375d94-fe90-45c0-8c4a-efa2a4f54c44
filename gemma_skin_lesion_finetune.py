#!/usr/bin/env python3
"""
Gemma 3N 4B Vision Fine-tuning for Skin Lesions Classification
Dataset: ahmed-ai/skin-lesions-classification-dataset

Adapted from: https://www.kaggle.com/code/danielhanchen/gemma-3n-4b-vision-finetuning
Author: Kaggle Adaptation for Skin Lesions
"""

# ==========================================
# SECTION 1: INSTALLATIONS AND IMPORTS
# ==========================================

# Install required packages
import subprocess
import sys

def install_packages():
    """Install required packages for fine-tuning"""
    packages = [
        "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git",
        "datasets>=2.16.0",
        "transformers>=4.40.0",
        "torch>=2.1.0",
        "Pillow>=9.0.0",
        "accelerate>=0.26.0",
        "wandb",
        "matplotlib",
        "seaborn"
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ Installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install {package}: {e}")

# Run installation
print("Installing required packages...")
install_packages()
print("Installation completed!")

# Import libraries
import os
import gc
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings("ignore")

# Unsloth and transformers imports
from unsloth import FastVisionModel, is_bfloat16_supported
from unsloth.trainer import UnslothVisionDataCollator
from transformers import TrainingArguments, TextStreamer
from datasets import load_dataset, Dataset
import transformers
from trl import SFTTrainer

print("All imports successful!")

# ==========================================
# SECTION 2: CONFIGURATION AND SETUP
# ==========================================

class Config:
    """Configuration class for training parameters"""
    
    # Model settings
    MODEL_NAME = "unsloth/gemma-3n-E4B-it"  # or "unsloth/gemma-3n-E4B"
    MAX_SEQ_LENGTH = 2048
    DTYPE = None  # Auto detection
    LOAD_IN_4BIT = True
    
    # Dataset settings
    DATASET_NAME = "ahmed-ai/skin-lesions-classification-dataset"
    NUM_SAMPLES = 1000  # Training with 1000 samples as requested
    TRAIN_SPLIT = 0.8
    VAL_SPLIT = 0.2
    
    # Training settings
    PER_DEVICE_TRAIN_BATCH_SIZE = 2
    GRADIENT_ACCUMULATION_STEPS = 4
    WARMUP_STEPS = 5
    MAX_STEPS = 250  # Adjust based on dataset size
    LEARNING_RATE = 2e-4
    FP16 = not is_bfloat16_supported()
    BF16 = is_bfloat16_supported()
    LOGGING_STEPS = 1
    OPTIM = "adamw_8bit"
    WEIGHT_DECAY = 0.01
    LR_SCHEDULER_TYPE = "linear"
    SEED = 3407
    OUTPUT_DIR = "./results"
    
    # Vision fine-tuning
    FINETUNE_VISION_LAYERS = True
    
    # Skin lesion classes
    SKIN_LESION_CLASSES = [
        "Actinic keratoses",
        "Basal cell carcinoma", 
        "Benign keratosis-like-lesions",
        "Chickenpox",
        "Cowpox",
        "Dermatofibroma",
        "Healthy",
        "HFMD",
        "Measles",
        "Melanocytic nevi",
        "Melanoma",
        "Monkeypox",
        "Squamous cell carcinoma",
        "Vascular lesions"
    ]

config = Config()
print(f"Configuration loaded. Model: {config.MODEL_NAME}")
print(f"Training samples: {config.NUM_SAMPLES}")
print(f"Classes: {len(config.SKIN_LESION_CLASSES)}")

# ==========================================
# SECTION 3: DATASET LOADING AND PREPROCESSING
# ==========================================

def load_skin_lesion_dataset():
    """Load and preprocess the skin lesions dataset"""
    print("Loading skin lesions dataset...")
    
    try:
        # Load the dataset
        dataset = load_dataset(config.DATASET_NAME)
        
        print(f"Dataset loaded successfully!")
        print(f"Dataset keys: {dataset.keys()}")
        print(f"Dataset info: {dataset}")
        
        # If dataset has train split, use it; otherwise use the main split
        if 'train' in dataset:
            data = dataset['train']
        else:
            # Assume the dataset has a default split
            data = dataset[list(dataset.keys())[0]]
        
        print(f"Total samples in dataset: {len(data)}")
        
        # Limit to specified number of samples
        if len(data) > config.NUM_SAMPLES:
            # Convert to pandas for easier sampling
            df = data.to_pandas()
            
            # Check unique labels in the data
            unique_labels = sorted(df['label'].unique())
            print(f"Unique labels found in dataset: {unique_labels}")
            
            # Get samples per class (distribute evenly)
            samples_per_class = config.NUM_SAMPLES // len(unique_labels)
            remaining_samples = config.NUM_SAMPLES % len(unique_labels)
            
            sampled_data = []
            
            for i, label_idx in enumerate(unique_labels):
                class_data = df[df['label'] == label_idx]
                n_samples = samples_per_class + (1 if i < remaining_samples else 0)
                
                if len(class_data) >= n_samples:
                    sampled_class = class_data.sample(n=n_samples, random_state=config.SEED)
                else:
                    sampled_class = class_data
                    print(f"Warning: Label {label_idx} has only {len(class_data)} samples (needed {n_samples})")
                
                sampled_data.append(sampled_class)
                print(f"Label {label_idx}: sampled {len(sampled_class)} samples")
            
            df_sampled = pd.concat(sampled_data, ignore_index=True)
            
            # Convert back to Dataset
            from datasets import Dataset
            data = Dataset.from_pandas(df_sampled)
        
        print(f"Using {len(data)} samples for training")
        
        # Convert label to ClassLabel for stratification
        from datasets import ClassLabel
        
        # Check if label needs conversion
        if not isinstance(data.features['label'], ClassLabel):
            print("Converting label column to ClassLabel for stratification...")
            
            # Create ClassLabel feature
            class_label_feature = ClassLabel(names=config.SKIN_LESION_CLASSES)
            
            # Cast the label column
            data = data.cast_column('label', class_label_feature)
            print("Label column converted successfully!")
        
        # Split data with stratification
        split_data = data.train_test_split(
            test_size=config.VAL_SPLIT, 
            seed=config.SEED,
            stratify_by_column='label'
        )
        
        train_data = split_data['train']
        val_data = split_data['test']
        
        print(f"Train samples: {len(train_data)}")
        print(f"Validation samples: {len(val_data)}")
        
        return train_data, val_data
        
    except Exception as e:
        print(f"Error loading dataset: {e}")
        raise

def analyze_dataset(train_data, val_data):
    """Analyze the dataset distribution"""
    print("\n" + "="*50)
    print("DATASET ANALYSIS")
    print("="*50)
    
    # Convert to pandas for analysis
    train_df = train_data.to_pandas()
    val_df = val_data.to_pandas()
    
    # Get unique labels and create mapping
    all_labels = sorted(set(train_df['label'].unique()) | set(val_df['label'].unique()))
    
    # Create label to class name mapping (use available class names or generic names)
    label_names = {}
    for label in all_labels:
        if label < len(config.SKIN_LESION_CLASSES):
            label_names[label] = config.SKIN_LESION_CLASSES[label]
        else:
            label_names[label] = f"Class_{label}"
    
    # Class distribution
    print("\nClass distribution in training set:")
    train_dist = train_df['label'].value_counts().sort_index()
    for label, count in train_dist.items():
        class_name = label_names.get(label, f"Unknown_{label}")
        print(f"  {label}: {class_name}: {count} samples")
    
    print("\nClass distribution in validation set:")
    val_dist = val_df['label'].value_counts().sort_index()
    for label, count in val_dist.items():
        class_name = label_names.get(label, f"Unknown_{label}")
        print(f"  {label}: {class_name}: {count} samples")
    
    # Plot distribution
    plt.figure(figsize=(15, 6))
    
    plt.subplot(1, 2, 1)
    labels_list = list(train_dist.index)
    values_list = list(train_dist.values)
    plt.bar(range(len(labels_list)), values_list)
    plt.title('Training Set Distribution')
    plt.xlabel('Class Index')
    plt.ylabel('Number of Samples')
    plt.xticks(range(len(labels_list)), [str(l) for l in labels_list], rotation=45)
    
    plt.subplot(1, 2, 2)
    val_labels_list = list(val_dist.index)
    val_values_list = list(val_dist.values)
    plt.bar(range(len(val_labels_list)), val_values_list)
    plt.title('Validation Set Distribution')
    plt.xlabel('Class Index')
    plt.ylabel('Number of Samples')
    plt.xticks(range(len(val_labels_list)), [str(l) for l in val_labels_list], rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # Show sample images from available classes
    print("\nSample images from each class:")
    available_classes = sorted(train_df['label'].unique())
    n_classes_to_show = min(len(available_classes), 14)
    
    cols = min(7, n_classes_to_show)
    rows = (n_classes_to_show + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(20, 4*rows))
    if rows == 1:
        axes = [axes] if n_classes_to_show == 1 else axes
    else:
        axes = axes.flatten()
    
    for i, class_idx in enumerate(available_classes[:n_classes_to_show]):
        class_samples = train_df[train_df['label'] == class_idx]
        if len(class_samples) > 0:
            sample = class_samples.iloc[0]
            image = sample['image']

            # Debug: print image type and properties
            print(f"Debug - Class {class_idx}: Image type: {type(image)}")

            # Convert image to numpy array with proper handling
            try:
                if hasattr(image, 'convert'):  # PIL Image
                    pil_image = image.convert('RGB')
                    image_array = np.array(pil_image, dtype=np.uint8)
                elif isinstance(image, np.ndarray):
                    if image.dtype == object:
                        # If it's an object array, try to extract the actual image
                        if hasattr(image.item(), 'convert'):
                            pil_image = image.item().convert('RGB')
                            image_array = np.array(pil_image, dtype=np.uint8)
                        else:
                            print(f"Warning: Object array contains non-PIL data for class {class_idx}")
                            continue
                    else:
                        image_array = image.astype(np.uint8)
                else:
                    # Try to convert to PIL first, then to numpy
                    try:
                        from PIL import Image as PILImage
                        if isinstance(image, str):
                            pil_image = PILImage.open(image).convert('RGB')
                        else:
                            pil_image = PILImage.fromarray(np.array(image)).convert('RGB')
                        image_array = np.array(pil_image, dtype=np.uint8)
                    except:
                        print(f"Warning: Could not convert image for class {class_idx} to numpy array")
                        continue

                print(f"Debug - Class {class_idx}: Final array shape: {image_array.shape}, dtype: {image_array.dtype}")

                class_name = label_names.get(class_idx, f"Class_{class_idx}")

                axes[i].imshow(image_array)
                axes[i].set_title(f"{class_idx}: {class_name}", fontsize=8)
                axes[i].axis('off')

            except Exception as e:
                print(f"Error processing image for class {class_idx}: {e}")
                continue
    
    # Hide unused subplots
    for i in range(n_classes_to_show, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()

# Load the dataset
train_dataset, val_dataset = load_skin_lesion_dataset()
analyze_dataset(train_dataset, val_dataset)

# ==========================================
# SECTION 4: MODEL LOADING AND SETUP
# ==========================================

def load_model_and_tokenizer():
    """Load the Gemma 3N model and tokenizer"""
    print(f"\nLoading model: {config.MODEL_NAME}")
    
    model, tokenizer = FastVisionModel.from_pretrained(
        model_name=config.MODEL_NAME,
        max_seq_length=config.MAX_SEQ_LENGTH,
        dtype=config.DTYPE,
        load_in_4bit=config.LOAD_IN_4BIT,
    )
    
    print("Model and tokenizer loaded successfully!")
    
    # Add LoRA adapters
    model = FastVisionModel.get_peft_model(
        model,
        finetune_vision_layers=config.FINETUNE_VISION_LAYERS,  # Enable vision layer fine-tuning
        finetune_language_layers=True,
        finetune_attention_modules=True,
        finetune_mlp_modules=True,
        r=16,  # LoRA rank
        lora_alpha=16,
        lora_dropout=0,
        bias="none",
        random_state=config.SEED,
        use_rslora=False,
        loftq_config=None,
    )
    
    print("LoRA adapters added successfully!")
    print(f"Vision layers fine-tuning: {config.FINETUNE_VISION_LAYERS}")
    
    return model, tokenizer

model, tokenizer = load_model_and_tokenizer()

# ==========================================
# SECTION 5: DATA FORMATTING AND PREPARATION
# ==========================================

def create_skin_lesion_prompt(image, label):
    """Create a formatted prompt for skin lesion classification"""
    # Get class name, handling cases where label might be outside predefined classes
    if label < len(config.SKIN_LESION_CLASSES):
        class_name = config.SKIN_LESION_CLASSES[label]
    else:
        class_name = f"skin_lesion_type_{label}"
    
    prompt = f"""<image>

Analyze this dermatological image and classify the skin lesion.

Question: What type of skin lesion is shown in this image?

Answer: This image shows {class_name}."""
    
    return prompt

def format_dataset_for_training(dataset, tokenizer):
    """Format the dataset for training with proper prompts"""
    print("Formatting dataset for training...")
    
    def formatting_prompts_func(examples):
        images = examples["image"]
        labels = examples["label"]
        texts = []
        
        for image, label in zip(images, labels):
            text = create_skin_lesion_prompt(image, label)
            texts.append(text)
        
        return {
            "text": texts,
            "image": images
        }
    
    formatted_dataset = dataset.map(
        formatting_prompts_func,
        batched=True,
        remove_columns=dataset.column_names
    )
    
    print(f"Dataset formatted. Sample count: {len(formatted_dataset)}")
    return formatted_dataset

# Format datasets
print("\nFormatting training dataset...")
formatted_train_dataset = format_dataset_for_training(train_dataset, tokenizer)

print("Formatting validation dataset...")
formatted_val_dataset = format_dataset_for_training(val_dataset, tokenizer)

# Show a sample
print("\nSample formatted prompt:")
print("="*60)
sample = formatted_train_dataset[0]
print(sample["text"])
print("="*60)

# ==========================================
# SECTION 6: TRAINING SETUP AND EXECUTION
# ==========================================

def setup_training():
    """Setup training arguments and trainer"""
    print("Setting up training configuration...")
    
    training_args = TrainingArguments(
        per_device_train_batch_size=config.PER_DEVICE_TRAIN_BATCH_SIZE,
        per_device_eval_batch_size=config.PER_DEVICE_TRAIN_BATCH_SIZE,
        gradient_accumulation_steps=config.GRADIENT_ACCUMULATION_STEPS,
        warmup_steps=config.WARMUP_STEPS,
        max_steps=config.MAX_STEPS,
        learning_rate=config.LEARNING_RATE,
        fp16=config.FP16,
        bf16=config.BF16,
        logging_steps=config.LOGGING_STEPS,
        optim=config.OPTIM,
        weight_decay=config.WEIGHT_DECAY,
        lr_scheduler_type=config.LR_SCHEDULER_TYPE,
        seed=config.SEED,
        output_dir=config.OUTPUT_DIR,
        report_to="none",  # Disable wandb for now
        save_steps=50,
        eval_steps=50,
        evaluation_strategy="steps",
        save_total_limit=2,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        dataloader_num_workers=2,
        remove_unused_columns=False,
    )
    
    trainer = SFTTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=formatted_train_dataset,
        eval_dataset=formatted_val_dataset,
        dataset_text_field="text",
        data_collator=UnslothVisionDataCollator(model, tokenizer),
        args=training_args,
    )
    
    print("Training setup completed!")
    return trainer

# Setup trainer
trainer = setup_training()

# Display training info
print(f"\nTraining Configuration:")
print(f"  Model: {config.MODEL_NAME}")
print(f"  Training samples: {len(formatted_train_dataset)}")
print(f"  Validation samples: {len(formatted_val_dataset)}")
print(f"  Max steps: {config.MAX_STEPS}")
print(f"  Batch size: {config.PER_DEVICE_TRAIN_BATCH_SIZE}")
print(f"  Gradient accumulation: {config.GRADIENT_ACCUMULATION_STEPS}")
print(f"  Learning rate: {config.LEARNING_RATE}")
print(f"  Vision layers fine-tuning: {config.FINETUNE_VISION_LAYERS}")

# ==========================================
# SECTION 7: MODEL TRAINING
# ==========================================

def train_model(trainer):
    """Execute the training process"""
    print("\n" + "="*60)
    print("STARTING TRAINING")
    print("="*60)
    
    # Clear cache before training
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    
    # Start training
    trainer_stats = trainer.train()
    
    print("\n" + "="*60)
    print("TRAINING COMPLETED!")
    print("="*60)
    print(f"Training loss: {trainer_stats.training_loss:.4f}")
    print(f"Total steps: {trainer_stats.global_step}")
    
    return trainer_stats

# Start training
training_stats = train_model(trainer)

# ==========================================
# SECTION 8: MODEL EVALUATION AND TESTING
# ==========================================

def evaluate_model(model, tokenizer):
    """Evaluate the fine-tuned model"""
    print("\n" + "="*60)
    print("MODEL EVALUATION")
    print("="*60)
    
    # Set model to evaluation mode
    model.eval()
    
    # Test with a few samples
    test_samples = val_dataset.select(range(min(5, len(val_dataset))))
    
    correct_predictions = 0
    total_predictions = 0
    
    print("\nTesting model on validation samples:")
    print("-" * 50)
    
    for i, sample in enumerate(test_samples):
        image = sample['image']
        true_label = sample['label']
        true_class = config.SKIN_LESION_CLASSES[true_label]
        
        # Create test prompt (without answer)
        test_prompt = f"""<image>

Analyze this dermatological image and classify the skin lesion.

Question: What type of skin lesion is shown in this image?

Answer:"""
        
        # Generate prediction
        try:
            inputs = tokenizer([test_prompt], images=[image], return_tensors="pt")
            
            if torch.cuda.is_available():
                inputs = {k: v.cuda() if hasattr(v, 'cuda') else v for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=50,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract the answer part
            answer_start = generated_text.find("Answer:") + len("Answer:")
            predicted_answer = generated_text[answer_start:].strip()
            
            # Simple accuracy check (if prediction contains true class name)
            is_correct = true_class.lower() in predicted_answer.lower()
            if is_correct:
                correct_predictions += 1
            total_predictions += 1
            
            print(f"\nSample {i+1}:")
            print(f"  True class: {true_class}")
            print(f"  Predicted: {predicted_answer}")
            print(f"  Correct: {'✓' if is_correct else '✗'}")
            
        except Exception as e:
            print(f"Error processing sample {i+1}: {e}")
            continue
    
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    print(f"\nEvaluation Results:")
    print(f"  Accuracy: {accuracy:.2%} ({correct_predictions}/{total_predictions})")

# Evaluate the model
evaluate_model(model, tokenizer)

# ==========================================
# SECTION 9: MODEL SAVING
# ==========================================

def save_model(model, tokenizer, save_path="./fine_tuned_gemma_skin_lesions"):
    """Save the fine-tuned model"""
    print(f"\nSaving fine-tuned model to {save_path}...")
    
    try:
        # Save using Unsloth's method
        model.save_pretrained(save_path)
        tokenizer.save_pretrained(save_path)
        
        print("✓ Model saved successfully!")
        
        # Also save in merged format for easier deployment
        merged_path = f"{save_path}_merged"
        print(f"Saving merged model to {merged_path}...")
        
        model.save_pretrained_merged(
            merged_path,
            tokenizer,
            save_method="merged_16bit"
        )
        
        print("✓ Merged model saved successfully!")
        
        return save_path, merged_path
        
    except Exception as e:
        print(f"Error saving model: {e}")
        return None, None

# Save the model
save_path, merged_path = save_model(model, tokenizer)

# ==========================================
# SECTION 10: INTERACTIVE TESTING INTERFACE
# ==========================================

def create_test_interface():
    """Create an interface for testing the model with uploaded images"""
    print("\n" + "="*60)
    print("INTERACTIVE TESTING INTERFACE")
    print("="*60)
    
    def test_single_image(image_path_or_pil):
        """Test the model with a single image"""
        try:
            # Load image
            if isinstance(image_path_or_pil, str):
                image = Image.open(image_path_or_pil).convert('RGB')
            else:
                image = image_path_or_pil.convert('RGB')
            
            # Create prompt
            test_prompt = f"""<image>

Analyze this dermatological image and classify the skin lesion.

Question: What type of skin lesion is shown in this image?

Answer:"""
            
            # Generate prediction
            inputs = tokenizer([test_prompt], images=[image], return_tensors="pt")
            
            if torch.cuda.is_available():
                inputs = {k: v.cuda() if hasattr(v, 'cuda') else v for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=100,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract answer
            answer_start = generated_text.find("Answer:") + len("Answer:")
            prediction = generated_text[answer_start:].strip()
            
            # Display results
            plt.figure(figsize=(10, 6))

            plt.subplot(1, 2, 1)
            # Convert image to numpy array with proper handling
            try:
                if hasattr(image, 'convert'):  # PIL Image
                    image_array = np.array(image, dtype=np.uint8)
                elif isinstance(image, np.ndarray):
                    if image.dtype == object:
                        # If it's an object array, try to extract the actual image
                        if hasattr(image.item(), 'convert'):
                            image_array = np.array(image.item(), dtype=np.uint8)
                        else:
                            image_array = np.array(image, dtype=np.uint8)
                    else:
                        image_array = image.astype(np.uint8)
                else:
                    # Try to convert to numpy array
                    image_array = np.array(image, dtype=np.uint8)
            except Exception as e:
                print(f"Warning: Could not convert image properly: {e}")
                # Fallback: try to display as-is
                image_array = image

            plt.imshow(image_array)
            plt.title("Input Image")
            plt.axis('off')
            
            plt.subplot(1, 2, 2)
            plt.text(0.1, 0.5, f"Prediction:\n{prediction}", 
                    fontsize=12, wrap=True,
                    verticalalignment='center',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            plt.xlim(0, 1)
            plt.ylim(0, 1)
            plt.axis('off')
            plt.title("Model Prediction")
            
            plt.tight_layout()
            plt.show()
            
            return prediction
            
        except Exception as e:
            print(f"Error testing image: {e}")
            return None
    
    # Test with sample from validation set
    print("\nTesting with sample from validation dataset:")
    sample_idx = 0
    sample = val_dataset[sample_idx]
    sample_image = sample['image']
    true_label = config.SKIN_LESION_CLASSES[sample['label']]
    
    print(f"True label: {true_label}")
    prediction = test_single_image(sample_image)
    
    return test_single_image

# Create test interface
test_function = create_test_interface()

# ==========================================
# SECTION 11: FINAL SUMMARY AND INSTRUCTIONS
# ==========================================

def print_final_summary():
    """Print final summary and usage instructions"""
    print("\n" + "="*80)
    print("FINE-TUNING COMPLETED SUCCESSFULLY!")
    print("="*80)
    
    print(f"""
Training Summary:
├── Model: {config.MODEL_NAME}
├── Dataset: {config.DATASET_NAME}
├── Training samples: {len(formatted_train_dataset)}
├── Validation samples: {len(formatted_val_dataset)}
├── Classes: {len(config.SKIN_LESION_CLASSES)}
├── Training steps: {config.MAX_STEPS}
├── Vision layers fine-tuned: {config.FINETUNE_VISION_LAYERS}
└── Final training loss: {training_stats.training_loss:.4f}

Model saved to:
├── Original: {save_path}
└── Merged: {merged_path}

Skin Lesion Classes:
""")
    
    for i, class_name in enumerate(config.SKIN_LESION_CLASSES):
        print(f"  {i:2d}: {class_name}")
    
    print(f"""
Usage Instructions:
1. Load the model: FastVisionModel.from_pretrained("{save_path}")
2. Use test_function(image) to test with new images
3. The model can classify {len(config.SKIN_LESION_CLASSES)} types of skin lesions
4. Input images should be dermatological/clinical images

Next Steps:
- Test the model with your own dermatological images
- Evaluate on a larger test set for robust metrics
- Consider data augmentation for better generalization
- Deploy the model for clinical assistance (with proper validation)

⚠️  Important: This model is for research/educational purposes only.
   Always consult medical professionals for actual diagnosis.
""")

print_final_summary()

# ==========================================
# SECTION 12: UTILITY FUNCTIONS FOR DEPLOYMENT
# ==========================================

def load_trained_model(model_path):
    """Utility function to load the trained model for inference"""
    print(f"Loading trained model from {model_path}...")
    
    try:
        model, tokenizer = FastVisionModel.from_pretrained(
            model_path,
            max_seq_length=config.MAX_SEQ_LENGTH,
            dtype=config.DTYPE,
            load_in_4bit=config.LOAD_IN_4BIT,
        )
        
        print("✓ Model loaded successfully!")
        return model, tokenizer
        
    except Exception as e:
        print(f"Error loading model: {e}")
        return None, None

def batch_predict(model, tokenizer, images, batch_size=4):
    """Predict on a batch of images"""
    predictions = []
    
    for i in range(0, len(images), batch_size):
        batch_images = images[i:i+batch_size]
        batch_predictions = []
        
        for image in batch_images:
            test_prompt = f"""<image>

Analyze this dermatological image and classify the skin lesion.

Question: What type of skin lesion is shown in this image?

Answer:"""
            
            try:
                inputs = tokenizer([test_prompt], images=[image], return_tensors="pt")
                
                if torch.cuda.is_available():
                    inputs = {k: v.cuda() if hasattr(v, 'cuda') else v for k, v in inputs.items()}
                
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=50,
                        temperature=0.1,
                        do_sample=True,
                        pad_token_id=tokenizer.eos_token_id
                    )
                
                generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
                answer_start = generated_text.find("Answer:") + len("Answer:")
                prediction = generated_text[answer_start:].strip()
                
                batch_predictions.append(prediction)
                
            except Exception as e:
                print(f"Error processing image: {e}")
                batch_predictions.append("Error in prediction")
        
        predictions.extend(batch_predictions)
    
    return predictions

print("\n" + "="*60)
print("ALL SECTIONS COMPLETED!")
print("The model is ready for use. You can now:")
print("1. Test with new images using test_function(image)")
print("2. Load the model using load_trained_model(model_path)")
print("3. Use batch_predict() for multiple images")
print("="*60)