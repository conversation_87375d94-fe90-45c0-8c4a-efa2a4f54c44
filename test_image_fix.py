#!/usr/bin/env python3
"""
Test script to fix the image display issue
"""

import numpy as np
import matplotlib.pyplot as plt
from datasets import load_dataset
from PIL import Image
import pandas as pd

# Configuration
DATASET_NAME = "ahmed-ai/skin-lesions-classification-dataset"

def test_image_display():
    """Test loading and displaying images from the dataset"""
    print("Loading dataset...")
    
    try:
        # Load the dataset
        dataset = load_dataset(DATASET_NAME)
        
        print(f"Dataset loaded successfully!")
        print(f"Dataset keys: {dataset.keys()}")
        
        # Get a small sample from train set
        if 'train' in dataset:
            data = dataset['train']
        else:
            data = dataset[list(dataset.keys())[0]]
        
        print(f"Total samples: {len(data)}")
        
        # Convert to pandas for easier handling
        df = data.to_pandas()
        
        # Get first few samples
        print("\nTesting image display...")
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i in range(min(6, len(df))):
            sample = df.iloc[i]
            image = sample['image']
            label = sample['label']
            
            print(f"Sample {i}: Image type: {type(image)}")
            
            # Convert image to numpy array with robust handling
            try:
                if hasattr(image, 'convert'):  # PIL Image
                    print(f"  - PIL Image mode: {image.mode}, size: {image.size}")
                    pil_image = image.convert('RGB')
                    image_array = np.array(pil_image, dtype=np.uint8)
                elif isinstance(image, np.ndarray):
                    print(f"  - NumPy array shape: {image.shape}, dtype: {image.dtype}")
                    if image.dtype == object:
                        # If it's an object array, try to extract the actual image
                        if hasattr(image.item(), 'convert'):
                            pil_image = image.item().convert('RGB')
                            image_array = np.array(pil_image, dtype=np.uint8)
                        else:
                            print(f"  - Object array contains: {type(image.item())}")
                            continue
                    else:
                        image_array = image.astype(np.uint8)
                else:
                    print(f"  - Unknown type, trying conversion...")
                    # Try to convert to PIL first, then to numpy
                    try:
                        if isinstance(image, str):
                            pil_image = Image.open(image).convert('RGB')
                        else:
                            pil_image = Image.fromarray(np.array(image)).convert('RGB')
                        image_array = np.array(pil_image, dtype=np.uint8)
                    except Exception as conv_e:
                        print(f"  - Conversion failed: {conv_e}")
                        continue
                
                print(f"  - Final array shape: {image_array.shape}, dtype: {image_array.dtype}")
                
                # Display the image
                axes[i].imshow(image_array)
                axes[i].set_title(f"Sample {i}, Label: {label}")
                axes[i].axis('off')
                
            except Exception as e:
                print(f"  - Error processing sample {i}: {e}")
                axes[i].text(0.5, 0.5, f"Error\n{str(e)[:50]}", 
                           ha='center', va='center', transform=axes[i].transAxes)
                axes[i].axis('off')
        
        # Hide unused subplots
        for i in range(min(6, len(df)), 6):
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('test_images.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_image_display()
